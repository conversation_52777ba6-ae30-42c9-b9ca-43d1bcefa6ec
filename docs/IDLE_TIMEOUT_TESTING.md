# Idle-Based Session Timeout Testing Guide

## Overview

The application now implements an idle-based session timeout system that only starts the countdown timer when the user becomes idle, replacing the previous absolute timeout approach.

## Key Features

### 1. **Idle Detection**
- Monitors mouse movements, clicks, keyboard input, scrolling, and touch events
- Tracks tab/window focus changes
- Considers tab losing focus as part of idle detection

### 2. **Idle Timeout Logic**
- Starts the 5-minute countdown timer ONLY when user becomes idle
- Resets the timer to full duration when user becomes active again
- Each idle period starts a fresh 5-minute timer

### 3. **Scope**
- Only applies to logged-in users
- Respects backend-driven timeout duration configuration
- Maintains existing session timeout UI/modal components

## Testing the Implementation

### Development Environment Testing

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Navigate to the dashboard:**
   - Go to `http://localhost:3000/dashboard`
   - Log in if not already authenticated

3. **Use the Idle Timeout Tester:**
   - A debug panel will appear in the bottom-right corner (development only)
   - The tester uses a 6-second timeout for quick testing (0.1 minutes)

### Test Scenarios

#### Scenario 1: Basic Idle Detection
1. **Expected Behavior:** User becomes idle after 6 seconds of no activity
2. **Test Steps:**
   - Stop all mouse/keyboard activity
   - Watch the status change from "🟢 Active" to "😴 Idle"
   - Observe "Timeout Active" becomes "⏰ Yes"
   - See countdown timer showing remaining seconds
3. **Expected Result:** Session timeout modal appears after 6 seconds of idle time

#### Scenario 2: Activity Reset
1. **Expected Behavior:** Timer resets when user becomes active
2. **Test Steps:**
   - Wait for user to become idle (status shows "😴 Idle")
   - Move mouse or press a key before timeout completes
   - Observe status changes back to "🟢 Active"
   - See "Timeout Active" becomes "⏸️ No"
3. **Expected Result:** Timer resets and no timeout occurs

#### Scenario 3: Tab Focus Changes
1. **Expected Behavior:** Tab losing focus affects idle detection
2. **Test Steps:**
   - Switch to another tab or window
   - Observe "Tab Focused" changes to "👁️‍🗨️ No"
   - Switch back to the application tab
   - See "Tab Focused" changes to "👁️ Yes"
3. **Expected Result:** Tab focus changes are properly detected

#### Scenario 4: Manual Activity Trigger
1. **Expected Behavior:** Manual activity trigger resets idle state
2. **Test Steps:**
   - Wait for user to become idle
   - Click "Trigger Activity" button in the tester
   - Observe status changes to "🟢 Active"
3. **Expected Result:** Idle state is reset programmatically

### Production Testing

In production, the idle timeout uses the actual betshop settings (typically 5 minutes):

1. **Login to the application**
2. **Stop all activity for the configured timeout duration**
3. **Verify the session timeout modal appears**
4. **Test activity during idle period resets the timer**

## Implementation Details

### Files Modified/Created

1. **`shared/hooks/ui/useUserActivity.ts`**
   - Core user activity detection hook
   - Monitors various user interaction events
   - Provides idle state and activity tracking

2. **`shared/hooks/ui/useIdleSessionTimeout.ts`**
   - Idle-based session timeout logic
   - Integrates with existing betshop settings
   - Only starts countdown when user is idle

3. **`shared/stores/SessionTimeoutHandler.tsx`**
   - Updated to use idle-based timeout instead of absolute timeout
   - Maintains existing modal and redirect functionality

4. **`shared/components/debug/IdleTimeoutTester.tsx`**
   - Development-only testing component
   - Provides real-time monitoring and controls
   - Includes activity logs and status display

### Configuration Options

The idle timeout system supports several configuration options:

```typescript
// Custom timeout duration (overrides betshop settings)
customTimeoutMinutes?: number;

// Enable debug logging
debug?: boolean;

// Events to monitor for activity
events?: string[];

// Whether to track tab focus changes
trackFocus?: boolean;

// Throttle delay for activity events
throttleDelay?: number;
```

## Troubleshooting

### Common Issues

1. **Timeout not triggering:**
   - Verify user is authenticated
   - Check betshop settings are loaded
   - Ensure no modal is currently open

2. **Activity not detected:**
   - Check browser console for debug logs (development mode)
   - Verify event listeners are properly attached
   - Test different types of activity (mouse, keyboard, scroll)

3. **Timer not resetting:**
   - Ensure activity events are being throttled properly
   - Check for JavaScript errors in console
   - Verify the activity detection is working

### Debug Information

In development mode, the system logs detailed information:
- Activity detection events
- Idle state changes
- Timer start/stop events
- Tab focus changes

Check the browser console for `[UserActivity]` and `[IdleSessionTimeout]` log messages.

## Migration Notes

### Changes from Previous Implementation

1. **Timeout Trigger:** Changed from absolute time since login to idle-based detection
2. **Timer Reset:** Now resets to full duration when user becomes active
3. **Activity Monitoring:** Added comprehensive user activity detection
4. **Tab Focus:** Considers tab visibility in idle detection

### Backward Compatibility

- Existing session timeout modal and UI components unchanged
- Backend timeout duration configuration still respected
- Authentication flow and logout behavior preserved
- All existing timeout-related functionality maintained

## Performance Considerations

- Activity detection is throttled to prevent excessive event handling
- Event listeners use passive mode where possible
- Cleanup properly handled on component unmount
- Debug logging only enabled in development mode
