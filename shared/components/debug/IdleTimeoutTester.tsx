'use client';
import React, { useState, useCallback } from 'react';
import { useIdleSessionTimeout } from '@/shared/hooks/ui/useIdleSessionTimeout';
import { useUserActivity } from '@/shared/hooks/ui/useUserActivity';

interface IdleTimeoutTesterProps {
  /** Custom timeout in minutes for testing (default: 0.1 = 6 seconds) */
  testTimeoutMinutes?: number;
}

/**
 * Debug component to test idle timeout functionality
 * Only use this in development environment
 */
export function IdleTimeoutTester({ testTimeoutMinutes = 0.1 }: IdleTimeoutTesterProps) {
  const [modalOpen, setModalOpen] = useState(false);
  const [timeoutTriggered, setTimeoutTriggered] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  // Add log entry
  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]); // Keep last 20 logs
  }, []);

  // Handle timeout
  const handleTimeout = useCallback(() => {
    addLog('🚨 Session timeout triggered!');
    setTimeoutTriggered(true);
    setModalOpen(true);
  }, [addLog]);

  // Handle modal close
  const handleClose = useCallback(() => {
    addLog('Modal closed - resetting test');
    setModalOpen(false);
    setTimeoutTriggered(false);
  }, [addLog]);

  // Use the idle session timeout hook with debug enabled
  const {
    isIdle,
    isTabFocused,
    lastActivity,
    triggerActivity,
    timeoutMs,
    isTimeoutActive,
    getRemainingTime
  } = useIdleSessionTimeout(handleTimeout, modalOpen, {
    debug: true,
    customTimeoutMinutes: testTimeoutMinutes
  });

  // Use user activity hook for additional monitoring
  const activityState = useUserActivity({
    idleTimeout: testTimeoutMinutes * 60 * 1000,
    debug: true,
    throttleDelay: 500 // Faster throttle for testing
  });

  // Manual activity trigger
  const handleManualActivity = useCallback(() => {
    addLog('🖱️ Manual activity triggered');
    triggerActivity();
  }, [triggerActivity, addLog]);

  // Reset test
  const handleReset = useCallback(() => {
    addLog('🔄 Test reset');
    setTimeoutTriggered(false);
    setModalOpen(false);
    setLogs([]);
    triggerActivity();
  }, [triggerActivity, addLog]);

  // Format time
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // Calculate remaining time
  const remainingTime = getRemainingTime();
  const remainingSeconds = remainingTime ? Math.ceil(remainingTime / 1000) : null;

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg p-4 z-50">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
          Idle Timeout Tester
        </h3>
        <button
          onClick={handleReset}
          className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Reset
        </button>
      </div>

      {/* Status Display */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Status:</span>
          <span className={`font-medium ${isIdle ? 'text-orange-600' : 'text-green-600'}`}>
            {isIdle ? '😴 Idle' : '🟢 Active'}
          </span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Tab Focused:</span>
          <span className={`font-medium ${isTabFocused ? 'text-green-600' : 'text-gray-500'}`}>
            {isTabFocused ? '👁️ Yes' : '👁️‍🗨️ No'}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Timeout Active:</span>
          <span className={`font-medium ${isTimeoutActive ? 'text-red-600' : 'text-gray-500'}`}>
            {isTimeoutActive ? '⏰ Yes' : '⏸️ No'}
          </span>
        </div>

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Last Activity:</span>
          <span className="font-medium text-gray-800 dark:text-white">
            {formatTime(lastActivity)}
          </span>
        </div>

        {remainingSeconds !== null && isIdle && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600 dark:text-gray-400">Timeout in:</span>
            <span className="font-medium text-red-600">
              {remainingSeconds}s
            </span>
          </div>
        )}

        <div className="flex justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Timeout Duration:</span>
          <span className="font-medium text-gray-800 dark:text-white">
            {timeoutMs ? `${timeoutMs / 1000}s` : 'N/A'}
          </span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={handleManualActivity}
          className="flex-1 px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600"
        >
          Trigger Activity
        </button>
      </div>

      {/* Activity Logs */}
      <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
        <h4 className="text-sm font-medium text-gray-800 dark:text-white mb-2">
          Activity Log
        </h4>
        <div className="max-h-32 overflow-y-auto text-xs space-y-1">
          {logs.length === 0 ? (
            <p className="text-gray-500 italic">No activity logged yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-gray-700 dark:text-gray-300">
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Modal Simulation */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
              Session Timeout Test
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              The idle timeout has been triggered! In a real scenario, this would log you out.
            </p>
            <button
              onClick={handleClose}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Close & Reset
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
