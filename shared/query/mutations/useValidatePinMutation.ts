// shared/query/mutations/useValidatePinMutation.ts
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuthStore } from '@/shared/stores/authStore';
import { transformLoginUserData } from '@/shared/utils/transformUserData';

interface PinValidationCredentials {
    pin: string;
    id: number;
    email: string;
}

interface PinValidationResponse {
    success: number;
    message: string;
    record: {
        validPin: boolean;
        tokenType: string;
        token: string; // The actual JWT/auth token
        user: any; // Raw user data from API (snake_case) - will be transformed to User format
        tenant?: { id: number;[key: string]: any }; // Include tenant details if relevant to store
        permissions?: any; // Include permissions if relevant to store
    };
    count: number;
}

const validatePin = async (credentials: PinValidationCredentials): Promise<PinValidationResponse> => {
    // console.log('credentials: ', credentials);
    const response = await fetch(`${process.env.NEXT_PUBLIC_ADMIN_BACKEND_URL}/api/login/admin/validatePin`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
        },
        body: JSON.stringify(credentials),
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'PIN validation failed');
    }

    return response.json();
};

export const useValidatePinMutation = () => {
    const queryClient = useQueryClient();
    const setFullAuth = useAuthStore((state) => state.setFullAuth); // Action for full authentication

    return useMutation<PinValidationResponse, Error, PinValidationCredentials>({
        mutationFn: validatePin,
        onSuccess: (data) => {
            if (data.success === 1 && data.record.validPin) {
                const rawUser = data.record.user;

                // Extract tenant information from the response if available
                const tenantId = data.record.tenant?.id;

                // Transform user data from snake_case to camelCase for consistency
                const user = transformLoginUserData(rawUser, tenantId);

                setFullAuth(data.record.token, user); // Final authentication step
                queryClient.invalidateQueries({ queryKey: ['user'] }); // Invalidate user-related queries
                // Clear any 2FA specific state, though setFullAuth should handle it
            } else {
                throw new Error(data.message || 'PIN validation failed unexpectedly');
            }
        },
        onError: (_error) => {
            // console.error('PIN validation error:', error.message);
            // Do NOT clear full auth here, only clear if they explicitly cancel 2FA or re-login.
            // If the PIN is wrong, just show error on the 2FA page.
        },
    });
};
